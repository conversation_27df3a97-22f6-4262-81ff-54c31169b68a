<script lang="ts">
  import { But<PERSON> } from "$lib/components/ui/button";
  import { <PERSON>, CardHeader, CardContent } from "$lib/components/ui/card";
  import { Input } from "$lib/components/ui/input";
  import ActionCard from "./action-card.svelte";
  import { onMount } from "svelte";
  import { type ActionParams } from "$lib/types/action";
  import type { ActionChainStatus } from "$lib/types/action-chain";
  import { Save, Copy, FileX, Plus } from "lucide-svelte";
  import { t } from "$lib/i18n";
  import { registry } from "$lib/actions/registry";
  import { validateAllParams } from "$lib/utils/validation-utils";

  interface Action {
    id: string;
    name: string;
    description: string;
    params: ActionParams;
    isCollapsed: boolean;
    inputTypes: string[];
    outputTypes: string[];
  }

  let {
    actions,
    onRemoveAction,
    onMoveUpAction,
    onMoveDownAction,
    onToggleCollapseAction,
    onUpdateActionParams,

    onProcess,
    onReorderActions,
    onClearActions,
    onShowError,
    inputFileType,
    actionChainStatus,
    onSaveActionChain,
    onSaveAsActionChain,
    onNewActionChain,
    onClearActionChain,
  } = $props<{
    actions: Action[];
    onRemoveAction: (id: string) => void;
    onMoveUpAction: (id: string) => void;
    onMoveDownAction: (id: string) => void;
    onToggleCollapseAction: (id: string) => void;
    onUpdateActionParams: (id: string, params: ActionParams) => void;

    onProcess: () => Promise<void>;
    onReorderActions: (fromIndex: number, toIndex: number) => void;
    onClearActions: () => void;
    onShowError: (message: string) => void;
    inputFileType: string;
    actionChainStatus: ActionChainStatus;
    onSaveActionChain: () => void;
    onSaveAsActionChain: () => void;
    onNewActionChain: () => void;
    onClearActionChain: () => void;
  }>();

  let isProcessing = $state(false);
  let draggedIndex = $state(-1);
  let dragOverIndex = $state(-1);
  let showClearConfirm = $state(false);

  function handleDragStart(e: DragEvent, index: number) {
    draggedIndex = index;
    if (e.dataTransfer) {
      e.dataTransfer.effectAllowed = "move";
    }
  }

  function handleDragOver(e: DragEvent, index: number) {
    e.preventDefault();
    if (e.dataTransfer) {
      e.dataTransfer.dropEffect = "move";
    }
    dragOverIndex = index;
  }

  function handleDrop(e: DragEvent, index: number) {
    e.preventDefault();
    if (draggedIndex !== -1 && draggedIndex !== index) {
      onReorderActions(draggedIndex, index);
    }
    draggedIndex = -1;
    dragOverIndex = -1;
  }

  function handleDragEnd() {
    draggedIndex = -1;
    dragOverIndex = -1;
  }

  // 验证动作链的输入输出兼容性
  function validateActionChain(inputFileType: string): {
    isValid: boolean;
    errorMessage: string;
  } {
    if (actions.length === 0) {
      return { isValid: true, errorMessage: "" };
    }

    let currentInputType = inputFileType;

    for (let i = 0; i < actions.length; i++) {
      const action = actions[i];

      // 检查当前动作是否支持输入类型
      if (!action.inputTypes.includes(currentInputType)) {
        return {
          isValid: false,
          errorMessage: `动作"${action.name}"不支持输入类型"${currentInputType}"。支持的输入类型: ${action.inputTypes.join(", ")}`,
        };
      }

      // 更新下一个动作的输入类型为当前动作的输出类型
      if (action.outputTypes.length > 0) {
        currentInputType = action.outputTypes[0]; // 取第一个输出类型
      } else {
        return {
          isValid: false,
          errorMessage: `动作"${action.name}"没有定义输出类型`,
        };
      }
    }

    return { isValid: true, errorMessage: "" };
  }
</script>

<Card class="w-full">
  <CardHeader class="flex-none p-4 sm:p-6">
    <!-- 动作链状态栏 -->
    <div class="mb-4 p-3 bg-muted/50 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span class="text-sm font-medium">当前动作链:</span>
          <span class="text-sm">
            {#if actionChainStatus.name}
              {actionChainStatus.name}
              {#if actionChainStatus.hasUnsavedChanges}
                <span class="text-orange-500">*</span>
              {/if}
            {:else}
              <span class="text-muted-foreground">未保存</span>
            {/if}
          </span>
        </div>
        <div class="flex items-center space-x-2">
          <Button size="sm" variant="outline" onclick={onNewActionChain}>
            <Plus class="w-4 h-4 mr-1" />
            新建
          </Button>
          <Button
            size="sm"
            onclick={onSaveActionChain}
            disabled={actions.length === 0}
          >
            <Save class="w-4 h-4 mr-1" />
            保存
          </Button>
          <Button
            size="sm"
            variant="outline"
            onclick={onSaveAsActionChain}
            disabled={actions.length === 0}
          >
            <Copy class="w-4 h-4 mr-1" />
            另存为
          </Button>
          <Button
            size="sm"
            variant="outline"
            onclick={onClearActionChain}
            disabled={actions.length === 0}
          >
            <FileX class="w-4 h-4 mr-1" />
            清空
          </Button>
        </div>
      </div>
    </div>

    <div
      class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
    >
      <div>
        <h3 class="text-lg font-semibold">处理列表</h3>
        <p class="text-sm text-muted-foreground">
          {actions.length} 个动作 {actions.length > 0
            ? `(${actions.length} 个待处理)`
            : ""}
        </p>
      </div>
      {#if actions.length > 0}
        <div class="flex gap-2">
          <button
            class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3 sm:px-4 py-2"
            disabled={isProcessing}
            on:click={async () => {
              // 先清空之前的错误信息
              onShowError("");

              // 验证新动作系统的参数
              for (const action of actions) {
                // 检查是否是新动作系统的动作（有actionId属性）
                if (action.actionId) {
                  const actionDef = registry.getAction(action.actionId);
                  if (actionDef) {
                    // 优先使用动作特定的验证函数
                    if (actionDef.validate) {
                      const validation = actionDef.validate(action.params);
                      if (!validation.isValid) {
                        // 翻译错误信息
                        const translatedErrors = validation.errors.map(
                          (errorKey) => $t(errorKey)
                        );
                        onShowError(
                          `动作"${action.name}"参数验证失败: ${translatedErrors.join(", ")}`
                        );
                        return;
                      }
                    } else {
                      // 回退到通用验证
                      const validation = validateAllParams(
                        action.params,
                        actionDef.params
                      );
                      if (!validation.isValid) {
                        onShowError(
                          `动作"${action.name}"参数验证失败: ${validation.errors.join(", ")}`
                        );
                        return;
                      }
                    }
                  }
                }
              }

              // 验证图片选择
              const imageActions = actions.filter(
                (action: Action) =>
                  action.name === "添加封面图片" ||
                  action.name === "添加封底图片"
              );

              for (const action of imageActions) {
                if (!action.params.imageFile) {
                  onShowError(`请为"${action.name}"选择图片文件`);
                  return;
                }
              }

              // 验证图片转视频和批量图片转视频的自定义分辨率
              const imageToVideoActions = actions.filter(
                (action: Action) =>
                  action.name === "图片转视频" ||
                  action.name === "批量图片转视频"
              );

              for (const action of imageToVideoActions) {
                if (action.params.resolution === "custom") {
                  if (
                    !action.params.customWidth ||
                    !action.params.customHeight
                  ) {
                    onShowError(
                      `请为"${action.name}"输入自定义分辨率的宽度和高度`
                    );
                    return;
                  }
                  if (
                    action.params.customWidth <= 0 ||
                    action.params.customHeight <= 0
                  ) {
                    onShowError(
                      `请为"${action.name}"输入有效的自定义分辨率（宽度和高度必须大于0）`
                    );
                    return;
                  }
                }
              }

              // 验证音频文件选择
              const audioActions = actions.filter(
                (action: Action) =>
                  action.name === "添加背景音乐" || action.name === "替换音频"
              );

              for (const action of audioActions) {
                if (!action.params.audioFile) {
                  onShowError(`请为"${action.name}"选择音频文件`);
                  return;
                }
              }

              // 验证水印文件选择
              const watermarkActions = actions.filter(
                (action: Action) =>
                  (action.name === "图片水印" &&
                    !action.params.watermarkPath) ||
                  (action.name === "视频水印" && !action.params.watermarkFile)
              );

              for (const action of watermarkActions) {
                onShowError(`请为"${action.name}"选择水印文件`);
                return;
              }

              // 链式验证 - 需要从父组件获取输入文件类型
              // 这里暂时跳过，等父组件传递输入文件类型后再实现
              const validation = validateActionChain(inputFileType);
              if (!validation.isValid) {
                onShowError(validation.errorMessage);
                return;
              }

              isProcessing = true;
              try {
                await onProcess();
              } catch (error) {
                // 确保错误被正确传递
                const errorMsg =
                  error instanceof Error ? error.message : String(error);
                onShowError(errorMsg);
              } finally {
                isProcessing = false;
              }
            }}
          >
            {isProcessing ? "处理中..." : "开始处理"}
          </button>
        </div>
      {/if}
    </div>
  </CardHeader>

  {#if showClearConfirm}
    <div
      class="fixed inset-0 z-50 pointer-events-none flex items-center justify-center p-4"
    >
      <div
        class="bg-white dark:bg-zinc-900 rounded-lg shadow-xl p-4 sm:p-6 min-w-[260px] max-w-[90vw] border border-zinc-200 dark:border-zinc-700 pointer-events-auto"
      >
        <div class="mb-4 text-lg font-semibold">确认清空</div>
        <div class="mb-6 text-sm text-muted-foreground">
          确定要清空所有动作吗？此操作不可撤销。
        </div>
        <div class="flex justify-end gap-2">
          <button
            class="px-3 sm:px-4 py-2 rounded bg-zinc-200 dark:bg-zinc-700 text-zinc-800 dark:text-zinc-100 hover:bg-zinc-300 dark:hover:bg-zinc-600"
            on:click={() => (showClearConfirm = false)}>取消</button
          >
          <button
            class="px-3 sm:px-4 py-2 rounded bg-destructive text-destructive-foreground hover:bg-destructive/90"
            on:click={() => {
              onClearActions();
              showClearConfirm = false;
            }}>确认</button
          >
        </div>
      </div>
    </div>
  {/if}

  <CardContent class="p-4 sm:p-6">
    {#if actions.length === 0}
      <div class="flex items-center justify-center h-32 text-muted-foreground">
        <div class="text-center">
          <p class="text-sm">暂无处理动作</p>
          <p class="text-xs">从左侧选择动作添加到处理列表</p>
        </div>
      </div>
    {:else}
      <div class="space-y-4">
        {#each actions as action, index}
          <div
            class="relative transition-all duration-200"
            class:opacity-50={draggedIndex === index}
            class:scale-105={dragOverIndex === index}
            class:border-2={dragOverIndex === index}
            class:border-primary={dragOverIndex === index}
            class:rounded-lg={dragOverIndex === index}
            class:bg-accent={dragOverIndex === index}
            class:bg-opacity-50={dragOverIndex === index}
          >
            <div
              class="w-full cursor-move"
              draggable="true"
              on:dragstart={(e) => handleDragStart(e, index)}
              on:dragover={(e) => handleDragOver(e, index)}
              on:drop={(e) => handleDrop(e, index)}
              on:dragend={handleDragEnd}
            >
              <ActionCard
                {action}
                onRemove={onRemoveAction}
                onMoveUp={onMoveUpAction}
                onMoveDown={onMoveDownAction}
                onToggleCollapse={onToggleCollapseAction}
                onUpdateParams={onUpdateActionParams}
                t={$t}
              />
            </div>
            {#if index < actions.length - 1}
              <div class="flex justify-center mt-2">
                <div class="w-0.5 h-4 bg-border"></div>
              </div>
            {/if}
          </div>
        {/each}
      </div>
    {/if}
  </CardContent>
</Card>
