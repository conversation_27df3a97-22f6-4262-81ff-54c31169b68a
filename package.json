{"name": "VideoIDE", "version": "0.1.0", "description": "", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "tauri": "tauri"}, "license": "MIT", "dependencies": {"@crabnebula/tauri-plugin-drag": "^2.1.0", "@tailwindcss/vite": "^4.1.10", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "~2.2.2", "@tauri-apps/plugin-fs": "^2.3.0", "@tauri-apps/plugin-opener": "^2", "lucide-svelte": "^0.522.0", "svelte-i18n": "^4.0.1", "svelte-sonner": "^1.0.5", "tailwindcss": "^4.1.10"}, "devDependencies": {"@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.522.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.9.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tauri-apps/cli": "^2", "bits-ui": "^2.8.8", "clsx": "^2.1.1", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tw-animate-css": "^1.3.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}