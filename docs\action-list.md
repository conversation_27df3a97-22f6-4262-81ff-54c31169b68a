# 动作列表文档

本文档记录了VideoIDE项目中所有的动作分类(categories)和对应的动作(actions)。

## 概览

- **9个动作分类**
- **47个动作定义** (其中1个未实现)
- 支持视频、音频、图片处理

## 动作分类详情

### 1. 裁剪 (trim)
处理视频和音频的时间裁剪操作。

- **截掉头部** (`trim-start`)
  - 功能：从视频开头截掉指定时长
  - 参数：开始时长
  - 支持：视频、音频

- **截掉尾部** (`trim-end`)
  - 功能：从视频结尾截掉指定时长
  - 参数：结束时长
  - 支持：视频、音频

- **截取多片段合并** (`trim-segment`)
  - 功能：截取多个时间段并合并
  - 参数：时间段列表
  - 支持：视频、音频

- **剔除多片段后合并** (`exclude-segment`)
  - 功能：剔除多个时间段后合并剩余部分
  - 参数：时间段列表
  - 支持：视频、音频

- **裁剪画面** (`crop-video`) - 遗漏, 从变换类别移过来
  - 描述: 裁剪视频画面尺寸
  - 功能：裁剪视频画面
  - 参数：裁剪区域坐标和尺寸
  - 支持：视频

### 2. 颜色调整 (color)
视频颜色和画质调整功能。

- **调整亮度** (`adjust-brightness`)
  - 功能：调整视频亮度
  - 参数：亮度值 (-1.0 到 1.0)
  - 支持：视频

- **调整对比度** (`adjust-contrast`)
  - 功能：调整视频对比度
  - 参数：对比度值 (-1.0 到 1.0)
  - 支持：视频

- **调整饱和度** (`adjust-saturation`)
  - 功能：调整视频饱和度
  - 参数：饱和度值 (-1.0 到 1.0)
  - 支持：视频

- **调整色相** (`adjust-hue`)
  - 功能：调整视频色相
  - 参数：色相值 (-180 到 180)
  - 支持：视频

- **调整伽马值** (`adjust-gamma`)
  - 功能：调整视频伽马值
  - 参数：伽马值 (0.1 到 3.0)
  - 支持：视频

- **白平衡调整** (`white-balance`)
  - 功能：调整视频白平衡
  - 参数：色温值 (2000K 到 11000K)
  - 支持：视频

### 3. 音频处理 (audio)
音频相关的处理功能。

- **调整音量** (`adjust-volume`)
  - 功能：调整音频音量
  - 参数：音量倍数 (0 到 3.0)
  - 支持：视频、音频

- **提取音频** (`extract-audio`)
  - 功能：从视频中提取音频
  - 参数：输出格式 (MP3, WAV, AAC, FLAC, OGG, M4A)
  - 支持：视频 → 音频

- **添加背景音乐** (`add-background-music`)
  - 功能：为视频添加背景音乐
  - 参数：音乐文件、音量
  - 支持：视频

- **替换音频** (`replace-audio`)
  - 功能：替换视频的音频轨道
  - 参数：音频文件
  - 支持：视频

- **静音** (`mute-audio`) - 遗漏
  - 描述: 移除视频的音频
  - 功能：静音音频
  - 参数：无
  - 支持：视频

### 4. 变换 (transform)
视频的几何变换和时间变换。

- **反转视频** (`reverse-video`) - 移到特效类别
  - 功能：倒放视频
  - 参数：是否保留音频
  - 支持：视频

- **旋转视频** (`rotate-video`)
  - 功能：旋转视频画面
  - 参数：旋转角度 (90°, 180°, 270°, 自定义)
  - 支持：视频

- **缩放视频** (`scale-video`)
  - 功能：缩放视频尺寸
  - 参数：缩放比例 (0.1 到 5.0)
  - 支持：视频

- **调整播放速度** (`adjust-speed`) - 移到特效类别
  - 功能：调整视频播放速度
  - 参数：速度倍数 (0.1 到 10.0)
  - 支持：视频

- **裁剪画面** (`crop-video`) - 移到裁剪分类
  - 功能：裁剪视频画面
  - 参数：裁剪区域坐标和尺寸
  - 支持：视频

- **水平翻转** (`flip-horizontal`) - 遗漏

- **垂直翻转** (`flip-vertical`) - 遗漏

### 5. 特效 (effect)
视频特效处理。

- **淡入效果** (`fade-in`)
  - 功能：添加淡入效果
  - 参数：淡入时长 (0.1 到 10.0秒)
  - 支持：视频

- **淡出效果** (`fade-out`)
  - 功能：添加淡出效果
  - 参数：淡出时长 (0.1 到 10.0秒)
  - 支持：视频

- **模糊效果** (`blur-effect`)
  - 功能：添加模糊效果
  - 参数：模糊强度 (1 到 50)
  - 支持：视频

- **锐化效果** (`sharpen-effect`)
  - 功能：添加锐化效果
  - 参数：锐化强度 (0.1 到 5.0)
  - 支持：视频

- **灰度效果** (`grayscale`) - 移到滤镜类别
  - 功能：转换为灰度视频
  - 参数：无
  - 支持：视频

- **怀旧效果** (`sepia`) - 移到滤镜类别
  - 功能：添加怀旧色调
  - 参数：无
  - 支持：视频

- **马赛克效果** (`mosaic`) - 移到滤镜类别
  - 功能：添加马赛克效果
  - 参数：块大小 (2 到 50)
  - 支持：视频

### 6. 水印 (watermark)
为视频添加各种类型的水印。

- **文字水印** (`text-watermark`)
  - 功能：添加文字水印
  - 参数：文字内容、位置、字体大小、颜色、透明度
  - 支持：视频

- **图片水印** (`image-watermark`)
  - 功能：添加图片水印
  - 参数：图片文件、位置、宽度、透明度
  - 支持：视频

- **视频水印** (`video-watermark`) ⚠️ **未实现**
  - 功能：添加视频水印
  - 参数：视频文件、位置、宽度、透明度
  - 支持：视频
  - 状态：已定义但未在前端action系统中实现

### 7. 编码 (encode)
视频编码和格式转换。

- **转换格式** (`convert-format`)
  - 功能：转换视频格式
  - 参数：目标格式 (MP4, AVI, MOV, MKV, WEBM)
  - 支持：视频

- **压缩视频** (`compress-video`)
  - 功能：压缩视频文件大小
  - 参数：压缩质量 (1 到 51)
  - 支持：视频

- **调整码率** (`adjust-bitrate`)
  - 功能：调整视频码率
  - 参数：目标码率
  - 支持：视频

- **调整分辨率** (`adjust-resolution`)
  - 功能：调整视频分辨率
  - 参数：目标分辨率
  - 支持：视频

### 8. 图片相关 (image)
图片和视频之间的转换处理。

- **添加封面图片** (`add-cover-image`)
  - 功能：在视频开头添加封面图片
  - 参数：图片文件、显示时长
  - 支持：视频

- **添加封底图片** (`add-end-image`)
  - 功能：在视频结尾添加封底图片
  - 参数：图片文件、显示时长
  - 支持：视频

- **图片转视频** (`image-to-video`)
  - 功能：将单张图片转换为视频
  - 参数：时长、分辨率
  - 支持：图片 → 视频

- **批量图片转视频** (`batch-image-to-video`)
  - 功能：将多张图片转换为视频
  - 参数：时长模式、切换效果、分辨率
  - 支持：图片 → 视频

- **视频转图片** (`video-to-image`)
  - 功能：从视频提取图片帧
  - 参数：提取间隔、输出格式
  - 支持：视频 → 图片

- **视频转动图** (`video-to-gif`)
  - 功能：将视频转换为GIF或WebP动图
  - 参数：输出格式、帧率、循环次数
  - 支持：视频 → 图片

### 9. 滤镜 (filter)
各种视频滤镜效果。

- **灰度滤镜** (`grayscale`)
  - 功能：转换为灰度
  - 参数：无
  - 支持：视频

- **怀旧滤镜** (`sepia`)
  - 功能：怀旧色调
  - 参数：无
  - 支持：视频

- **浮雕滤镜** (`emboss`)
  - 功能：浮雕效果
  - 参数：无
  - 支持：视频

- **素描滤镜** (`sketch`)
  - 功能：素描效果
  - 参数：模式 (灰度/彩色)
  - 支持：视频

- **油画滤镜** (`oil-painting`)
  - 功能：油画效果
  - 参数：强度 (1.0 到 50.0)
  - 支持：视频

- **马赛克滤镜** (`mosaic`)
  - 功能：马赛克效果
  - 参数：块大小 (2 到 50)
  - 支持：视频

- **像素化滤镜** (`pixelate`)
  - 功能：像素化效果
  - 参数：像素大小 (2 到 50)
  - 支持：视频

- **边缘检测滤镜** (`edge-detection`)
  - 功能：边缘检测效果
  - 参数：无
  - 支持：视频

- **复古滤镜** (`vintage-filter`)
  - 功能：复古色调
  - 参数：无
  - 支持：视频

- **冷色调滤镜** (`cold-tone`)
  - 功能：冷色调效果
  - 参数：强度 (0.0 到 5.0)
  - 支持：视频

- **暖色调滤镜** (`warm-tone`)
  - 功能：暖色调效果
  - 参数：强度 (0.0 到 5.0)
  - 支持：视频

## 统计信息

### 按分类统计
- 裁剪类：4个动作
- 颜色调整类：6个动作
- 音频处理类：4个动作
- 变换类：5个动作
- 特效类：7个动作
- 水印类：3个动作 (1个未实现)
- 编码类：4个动作
- 图片相关类：6个动作
- 滤镜类：11个动作

### 按输入类型统计
- 支持视频输入：42个动作
- 支持音频输入：6个动作
- 支持图片输入：2个动作

### 按功能特性统计
- 支持预览：39个动作
- 不支持预览：8个动作

## 遗漏项目

### 已定义但未实现的动作
1. **视频水印** (`video-watermark`)
   - 在 `action-ids.ts` 中已定义
   - 在 `src-tauri/src/watermark/mod.rs` 中已有后端实现
   - 但在 `src/lib/actions/categories/watermark.ts` 中未添加到前端action系统

### 建议补充
建议将 `video-watermark` 动作添加到前端action系统中，以完善水印功能。

## 文件位置

### 前端定义文件
- 动作分类：`src/lib/actions/categories.ts`
- 动作常量：`src/lib/constants/action-ids.ts`
- 分类常量：`src/lib/constants/category-ids.ts`
- 具体实现：`src/lib/actions/categories/*.ts`

### 后端实现文件
- 裁剪：`src-tauri/src/trim/mod.rs`
- 颜色：`src-tauri/src/color/mod.rs`
- 音频：`src-tauri/src/audio/mod.rs`
- 变换：`src-tauri/src/transform/mod.rs`
- 特效：`src-tauri/src/effect/mod.rs`
- 水印：`src-tauri/src/watermark/mod.rs`
- 编码：`src-tauri/src/encode/mod.rs`
- 图片：`src-tauri/src/image/mod.rs`
- 滤镜：`src-tauri/src/filter/mod.rs`

---

*最后更新：2025-07-18*
