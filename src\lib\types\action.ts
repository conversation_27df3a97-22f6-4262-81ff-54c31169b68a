// 动作系统类型定义

// 媒体类型枚举
export type MediaType = "video" | "audio" | "image";

// 参数类型枚举
export type ParamType =
  | "number"
  | "string"
  | "boolean"
  | "file"
  | "select"
  | "range"
  | "color"
  | "duration"
  | "resolution"
  | "position";

// 参数定义接口
export interface ActionParamDefinition {
  key: string;
  type: ParamType;
  nameKey: string; // i18n key for parameter name
  descriptionKey?: string; // i18n key for parameter description
  required?: boolean;
  defaultValue?: any;
  min?: number;
  max?: number;
  step?: number;
  options?: Array<{ value: any; labelKey: string }>; // for select type
  validation?: (value: any) => boolean | string;
}

// 参数值接口
export interface ActionParams {
  [key: string]: any;
}

// 验证结果接口
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// 动作执行上下文接口
export interface ActionContext {
  inputPath: string; // 当前输入文件路径
  outputPath: string; // 输出文件路径
  isLastAction: boolean; // 是否是最后一个动作
  actionIndex: number; // 当前动作在链中的索引
  totalActions: number; // 总动作数量
  tempFiles: string[]; // 临时文件列表
}

// 动作类别接口
export interface Category {
  id: string;
  nameKey: string; // i18n key
  descriptionKey: string; // i18n key
  icon?: string;
  order: number;
}

// 动作接口
export interface Action {
  id: string;
  nameKey: string; // i18n key
  descriptionKey: string; // i18n key
  categoryId: string;
  inputTypes: MediaType[];
  outputTypes: MediaType[];
  params: ActionParamDefinition[];
  icon?: string;
  preview?: boolean; // 是否支持预览
  validate?: (params: ActionParams) => ValidationResult;
  order?: number;

  // 新增字段：后端调用相关
  invokeCommand: string; // 对应的后端服务名称
  buildParams: (params: ActionParams, context: ActionContext) => any; // 构建后端参数的函数
}

// 动作实例接口（用于动作链中）
export interface ActionInstance {
  id: string; // 实例ID，用于在动作链中唯一标识
  actionId: string; // 动作类型ID
  name: string; // 显示名称（可能是翻译后的）
  description: string; // 显示描述（可能是翻译后的）
  params: ActionParams;
  isCollapsed: boolean;
  inputTypes: MediaType[];
  outputTypes: MediaType[];
  order?: number;
}

// 动作模板接口（用于动作列表显示）
export interface ActionTemplate {
  id: string;
  name: string; // 显示名称（翻译后的）
  description: string; // 显示描述（翻译后的）
  category: string; // 类别名称（翻译后的）
  categoryId: string;
  icon?: string;
  inputTypes: MediaType[];
  outputTypes: MediaType[];
}

// 动作注册器接口
export interface ActionRegistry {
  categories: Map<string, Category>;
  actions: Map<string, Action>;

  registerCategory(category: Category): void;
  registerAction(action: Action): void;
  getCategory(id: string): Category | undefined;
  getAction(id: string): Action | undefined;
  getActionsByCategory(categoryId: string): Action[];
  getAllCategories(): Category[];
  getAllActions(): Action[];
  createActionInstance(actionId: string): ActionInstance | undefined;
}

// 权限相关接口
export interface Permission {
  actionIds: string[];
  categoryIds: string[];
}

export interface UserPermission {
  userId?: string;
  permission: Permission;
}
