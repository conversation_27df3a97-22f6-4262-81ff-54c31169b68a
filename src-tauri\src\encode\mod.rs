use serde::Serialize;
use std::path::Path;
use tauri::Emitter;

use crate::common::{
    get_video_info_internal, is_cancelled, kill_ffmpeg_process, mark_ffmpeg_finished,
    mark_ffmpeg_running, reset_cancelled,
};
use crate::config;

#[derive(Clone, Serialize)]
struct Payload {
    pct: f32,
}

// 转换格式
#[tauri::command]
pub async fn convert_format(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    format: String,
) -> Result<(), String> {
    println!("convert_format called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  format: {}", format);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始转换格式: {} -> {}", input_path, output_path);
    println!("目标格式: {}", format);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    // 根据格式选择合适的编码器
    let (video_codec, audio_codec) = match format.as_str() {
        "webm" => ("libvpx-vp9", "libopus"),
        "mkv" => ("libx264", "aac"),
        "avi" => ("libx264", "aac"),
        "mov" => ("libx264", "aac"),
        _ => ("libx264", "aac"), // 默认mp4
    };

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-c:v",
            video_codec,
            "-c:a",
            audio_codec,
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("ENCODE_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("格式转换已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("ENCODE_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 格式转换完成！");
    window
        .emit("ENCODE_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

/// 压缩参数结构体
#[derive(Debug)]
struct CompressionParams {
    preset: String,            // 编码预设
    crf: u8,                   // 恒定质量因子
    max_bitrate: String,       // 最大码率
    buffer_size: String,       // 缓冲区大小
    video_filter: String,      // 视频滤镜
    audio_bitrate: String,     // 音频码率
    audio_sample_rate: String, // 音频采样率
}

/// 根据压缩级别计算压缩参数
fn calculate_compression_params(level: i32) -> CompressionParams {
    match level {
        1 => CompressionParams {
            preset: "veryslow".to_string(),
            crf: 28,
            max_bitrate: "200k".to_string(),
            buffer_size: "400k".to_string(),
            video_filter: "scale=480:270".to_string(), // 降至270p
            audio_bitrate: "32k".to_string(),
            audio_sample_rate: "22050".to_string(),
        },
        2 => CompressionParams {
            preset: "veryslow".to_string(),
            crf: 26,
            max_bitrate: "400k".to_string(),
            buffer_size: "800k".to_string(),
            video_filter: "scale=640:360".to_string(), // 360p
            audio_bitrate: "48k".to_string(),
            audio_sample_rate: "22050".to_string(),
        },
        3 => CompressionParams {
            preset: "slow".to_string(),
            crf: 24,
            max_bitrate: "600k".to_string(),
            buffer_size: "1200k".to_string(),
            video_filter: "scale=854:480".to_string(), // 480p
            audio_bitrate: "64k".to_string(),
            audio_sample_rate: "44100".to_string(),
        },
        4 => CompressionParams {
            preset: "slow".to_string(),
            crf: 22,
            max_bitrate: "1000k".to_string(),
            buffer_size: "2000k".to_string(),
            video_filter: "scale=1280:720".to_string(), // 720p
            audio_bitrate: "96k".to_string(),
            audio_sample_rate: "44100".to_string(),
        },
        5 => CompressionParams {
            preset: "medium".to_string(),
            crf: 20,
            max_bitrate: "1500k".to_string(),
            buffer_size: "3000k".to_string(),
            video_filter: "scale=-2:720".to_string(), // 保持比例的720p
            audio_bitrate: "128k".to_string(),
            audio_sample_rate: "44100".to_string(),
        },
        6 => CompressionParams {
            preset: "medium".to_string(),
            crf: 18,
            max_bitrate: "2500k".to_string(),
            buffer_size: "5000k".to_string(),
            video_filter: "scale=-2:1080".to_string(), // 保持比例的1080p
            audio_bitrate: "128k".to_string(),
            audio_sample_rate: "48000".to_string(),
        },
        7 => CompressionParams {
            preset: "fast".to_string(),
            crf: 16,
            max_bitrate: "4000k".to_string(),
            buffer_size: "8000k".to_string(),
            video_filter: "scale=-2:1080".to_string(),
            audio_bitrate: "192k".to_string(),
            audio_sample_rate: "48000".to_string(),
        },
        8 => CompressionParams {
            preset: "fast".to_string(),
            crf: 14,
            max_bitrate: "6000k".to_string(),
            buffer_size: "12000k".to_string(),
            video_filter: "scale=-2:1080".to_string(),
            audio_bitrate: "256k".to_string(),
            audio_sample_rate: "48000".to_string(),
        },
        9 => CompressionParams {
            preset: "veryfast".to_string(),
            crf: 12,
            max_bitrate: "10000k".to_string(),
            buffer_size: "20000k".to_string(),
            video_filter: "scale=-2:1080".to_string(),
            audio_bitrate: "320k".to_string(),
            audio_sample_rate: "48000".to_string(),
        },
        10 => CompressionParams {
            preset: "veryfast".to_string(),
            crf: 10,
            max_bitrate: "15000k".to_string(),
            buffer_size: "30000k".to_string(),
            video_filter: "scale=-2:1080".to_string(), // 基本不压缩分辨率
            audio_bitrate: "320k".to_string(),
            audio_sample_rate: "48000".to_string(),
        },
        _ => CompressionParams {
            preset: "medium".to_string(),
            crf: 20,
            max_bitrate: "1500k".to_string(),
            buffer_size: "3000k".to_string(),
            video_filter: "scale=-2:720".to_string(),
            audio_bitrate: "128k".to_string(),
            audio_sample_rate: "44100".to_string(),
        },
    }
}

// 压缩视频
#[tauri::command]
pub async fn compress_video(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    compression_level: i32,
) -> Result<(), String> {
    println!("compress_video called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  compression_level: {}", compression_level);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 验证压缩级别
    if compression_level < 1 || compression_level > 10 {
        return Err("压缩级别必须在1-10之间".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始压缩视频: {} -> {}", input_path, output_path);
    println!("压缩级别: {}", compression_level);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    // 根据压缩级别计算压缩参数
    let params = calculate_compression_params(compression_level);

    println!("压缩参数:");
    println!("  编码预设: {}", params.preset);
    println!("  CRF值: {}", params.crf);
    println!("  最大码率: {}", params.max_bitrate);
    println!("  视频滤镜: {}", params.video_filter);
    println!("  音频码率: {}", params.audio_bitrate);

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-y", // 覆盖输出文件
            "-c:v",
            "libx264", // 使用 H.264 编码器
            "-preset",
            &params.preset, // 编码预设
            "-crf",
            &params.crf.to_string(), // 恒定质量因子
            "-maxrate",
            &params.max_bitrate, // 最大码率
            "-bufsize",
            &params.buffer_size, // 缓冲区大小
            "-vf",
            &params.video_filter, // 视频滤镜 (分辨率缩放)
            "-c:a",
            "aac", // 音频编码器
            "-b:a",
            &params.audio_bitrate, // 音频码率
            "-ac",
            "2", // 音频声道数
            "-ar",
            &params.audio_sample_rate, // 音频采样率
            "-movflags",
            "+faststart", // 优化流媒体播放
            "-pix_fmt",
            "yuv420p", // 像素格式
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("ENCODE_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("视频压缩已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("ENCODE_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 视频压缩完成！");
    window
        .emit("ENCODE_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 调整码率
#[tauri::command]
pub async fn adjust_bitrate(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    bitrate: String,
) -> Result<(), String> {
    println!("adjust_bitrate called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  bitrate: {}", bitrate);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始调整码率: {} -> {}", input_path, output_path);
    println!("目标码率: {}", bitrate);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-c:v",
            "libx264",
            "-b:v",
            &bitrate,
            "-c:a",
            "aac",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("ENCODE_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("码率调整已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("ENCODE_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 码率调整完成！");
    window
        .emit("ENCODE_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}

// 调整分辨率
#[tauri::command]
pub async fn adjust_resolution(
    window: tauri::Window,
    input_path: String,
    output_path: String,
    width: i32,
    height: i32,
) -> Result<(), String> {
    println!("adjust_resolution called with:");
    println!("  input_path: {}", input_path);
    println!("  output_path: {}", output_path);
    println!("  width: {}", width);
    println!("  height: {}", height);

    // 重置取消标志
    reset_cancelled();

    // 检查输入文件是否存在
    if !Path::new(&input_path).exists() {
        return Err("输入文件不存在".to_string());
    }

    // 验证分辨率参数
    if width <= 0 || height <= 0 {
        return Err("分辨率参数必须大于0".to_string());
    }

    // 创建输出目录（如果不存在）
    if let Some(parent) = Path::new(&output_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| e.to_string())?;
    }

    println!("开始调整分辨率: {} -> {}", input_path, output_path);
    println!("目标分辨率: {}x{}", width, height);

    // 获取视频信息来计算进度
    let video_info = get_video_info_internal(&input_path).await?;
    let total_frames = (video_info.duration * 30.0) as u64; // 假设30fps用于进度计算

    let mut ffmpeg = config::create_ffmpeg_command()?
        .args([
            "-i",
            &input_path,
            "-vf",
            &format!("scale={}:{}", width, height),
            "-c:v",
            "libx264",
            "-c:a",
            "aac",
            "-y",
            &output_path,
        ])
        .spawn()
        .map_err(|e| {
            println!("[FFmpeg spawn error] {e:?}");
            format!("FFmpeg启动失败: {e}")
        })?;

    // 标记ffmpeg进程开始运行
    mark_ffmpeg_running();

    // 发送开始处理事件
    window
        .emit("ENCODE_PROGRESS", Payload { pct: 0.0 })
        .unwrap();

    // 推送进度
    let mut last_pct = 0.0f32;
    for progress in ffmpeg
        .iter()
        .map_err(|e| format!("FFmpeg处理失败: {}", e))?
        .filter_progress()
    {
        if is_cancelled() {
            println!("分辨率调整已取消");
            kill_ffmpeg_process();
            return Err("处理已取消".to_string());
        }

        let frame = progress.frame;
        let pct = ((frame as f64 * 100.0) / total_frames as f64).min(100.0) as f32;
        // 只在进度有明显变化时推送，减少事件数量
        if (pct - last_pct).abs() >= 1.0 || pct == 100.0 {
            let _ = window.emit("ENCODE_PROGRESS", Payload { pct });
            last_pct = pct;
        }
    }

    // 标记ffmpeg进程结束
    mark_ffmpeg_finished();

    println!("✅ 分辨率调整完成！");
    window
        .emit("ENCODE_PROGRESS", Payload { pct: 100.0 })
        .unwrap();
    Ok(())
}
