<script lang="ts">
  import { cn } from "$lib/utils";

  interface Props {
    value?: string;
    onValueChange?: (value: string) => void;
    disabled?: boolean;
    class?: string;
    children?: any;
  }

  let {
    value = $bindable(),
    onValueChange,
    disabled = false,
    class: className,
    children,
    ...props
  }: Props = $props();

  function handleChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    const newValue = target.value;
    value = newValue;
    onValueChange?.(newValue);
  }
</script>

<select
  {value}
  {disabled}
  onchange={handleChange}
  class={cn(
    "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
    className
  )}
  {...props}
>
  {@render children?.()}
</select>
