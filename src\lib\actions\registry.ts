// 动作注册中心

import { writable, derived, type Readable } from "svelte/store";
import type {
  Action,
  ActionInstance,
  ActionRegistry,
  Category,
  ActionTemplate,
  Permission,
  UserPermission,
} from "../types/action";
import { createDefaultParams } from "./utils";

/**
 * 动作注册中心实现
 */
class ActionRegistryImpl implements ActionRegistry {
  categories = new Map<string, Category>();
  actions = new Map<string, Action>();

  // 注册类别
  registerCategory(category: Category): void {
    this.categories.set(category.id, category);
  }

  // 注册动作
  registerAction(action: Action): void {
    this.actions.set(action.id, action);
  }

  // 获取类别
  getCategory(id: string): Category | undefined {
    return this.categories.get(id);
  }

  // 获取动作
  getAction(id: string): Action | undefined {
    return this.actions.get(id);
  }

  // 获取指定类别的所有动作
  getActionsByCategory(categoryId: string): Action[] {
    return Array.from(this.actions.values())
      .filter((action) => action.categoryId === categoryId)
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  }

  // 获取所有类别
  getAllCategories(): Category[] {
    return Array.from(this.categories.values()).sort(
      (a, b) => a.order - b.order
    );
  }

  // 获取所有动作
  getAllActions(): Action[] {
    return Array.from(this.actions.values());
  }

  // 创建动作实例
  createActionInstance(actionId: string): ActionInstance | undefined {
    const action = this.getAction(actionId);
    if (!action) return undefined;

    return {
      id: crypto.randomUUID(), // 生成唯一ID
      actionId: action.id,
      name: action.nameKey, // 这里使用nameKey，后续会被i18n系统翻译
      description: action.descriptionKey, // 这里使用descriptionKey，后续会被i18n系统翻译
      params: createDefaultParams(action.params),
      isCollapsed: false,
      inputTypes: action.inputTypes,
      outputTypes: action.outputTypes,
    };
  }
}

// 创建全局注册中心实例
const registry = new ActionRegistryImpl();

// 创建权限存储
const permissionStore = writable<UserPermission>({
  permission: {
    actionIds: [], // 空数组表示没有限制
    categoryIds: [], // 空数组表示没有限制
  },
});

// 创建过滤后的动作和类别存储
const filteredActions = derived(permissionStore, ($permissions) => {
  const { actionIds, categoryIds } = $permissions.permission;

  // 如果权限列表为空，表示没有限制
  const hasActionRestrictions = actionIds.length > 0;
  const hasCategoryRestrictions = categoryIds.length > 0;

  if (!hasActionRestrictions && !hasCategoryRestrictions) {
    // 没有限制，返回所有动作
    return registry.getAllActions();
  }

  return registry.getAllActions().filter((action) => {
    // 检查动作ID是否在允许列表中
    if (hasActionRestrictions && !actionIds.includes(action.id)) {
      return false;
    }

    // 检查类别ID是否在允许列表中
    if (hasCategoryRestrictions && !categoryIds.includes(action.categoryId)) {
      return false;
    }

    return true;
  });
});

const filteredCategories = derived(
  [permissionStore, filteredActions],
  ([$permissions, $actions]) => {
    const { categoryIds } = $permissions.permission;

    // 如果没有类别限制且没有动作
    if (categoryIds.length === 0 && $actions.length === 0) {
      return registry.getAllCategories();
    }

    // 获取所有有权限的动作的类别ID
    const allowedCategoryIds = new Set(
      $actions.map((action) => action.categoryId)
    );

    // 如果有类别限制，添加到允许列表
    if (categoryIds.length > 0) {
      categoryIds.forEach((id) => allowedCategoryIds.add(id));
    }

    // 过滤类别
    return registry
      .getAllCategories()
      .filter((category) => allowedCategoryIds.has(category.id));
  }
);

// 更新权限
function updatePermissions(permissions: UserPermission): void {
  permissionStore.set(permissions);
}

// 创建动作模板（用于UI显示）
function createActionTemplates(
  t: (key: string) => string // i18n翻译函数
): ActionTemplate[] {
  // 获取当前的动作列表
  let currentActions: Action[] = [];
  filteredActions.subscribe((actions) => {
    currentActions = actions;
  })();

  return currentActions.map((action: Action) => {
    const category = registry.getCategory(action.categoryId);
    return {
      id: action.id,
      name: t(action.nameKey),
      description: t(action.descriptionKey),
      category: category ? t(category.nameKey) : "",
      categoryId: action.categoryId,
      icon: action.icon,
      inputTypes: action.inputTypes,
      outputTypes: action.outputTypes,
    };
  });
}

// 导出
export {
  registry,
  filteredActions,
  filteredCategories,
  updatePermissions,
  createActionTemplates,
};
